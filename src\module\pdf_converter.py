
import pymupdf4llm
import pymupdf
import json
import logging
from dataclasses import dataclass
from typing import List, Dict, Any
import re
from pathlib import Path
import time
import asyncio
import subprocess
import concurrent.futures
import shutil
import os
from concurrent.futures import ProcessPoolExecutor, as_completed

kangxi_map = {"⼀": "一","⼄": "乙","⼆": "二","⼈": "人","⼉": "儿","⼊": "入","⼋": "八","⼏": "几","⼑": "刀","⼒": "力","⼔": "匕","⼗": "十","⼘": "卜","⼚": "厂","⼜": "又","⼝": "口","⼞": "口","⼟": "土","⼠": "士","⼣": "夕","⼤": "大","⼥": "女","⼦": "子","⼨": "寸","⼩": "小","⼫": "尸","⼭": "山","⼯": "工","⼰": "己","⼲": "干","⼴": "广","⼸": "弓","⼼": "心","⼽": "戈","⼿": "手","⽀": "支","⽂": "文","⽃": "斗","⽄": "斤","⽅": "方","⽆": "无","⽇": "日","⽈": "曰","⽉": "月","⽊": "木","⽋": "欠","⽌": "止","⽍": "歹","⽏": "毋","⽐": "比","⽑": "毛","⽒": "氏","⽓": "气","⽔": "水","⽕": "火","⽖": "爪","⽗": "父","⽚": "片","⽛": "牙","⽜": "牛","⽝": "犬","⽞": "玄","⽟": "玉","⽠": "瓜","⽡": "瓦","⽢": "甘","⽣": "生","⽤": "用","⽥": "田","⽩": "白","⽪": "皮","⽫": "皿","⽬": "目","⽭": "矛","⽮": "矢","⽯": "石","⽰": "示","⽲": "禾","⽳": "穴","⽴": "立","⽵": "竹","⽶": "米","⽸": "缶","⽹": "网","⽺": "羊","⽻": "羽","⽼": "老","⽽": "而","⽿": "耳","⾁": "肉","⾂": "臣","⾃": "自","⾄": "至","⾆": "舌","⾈": "舟","⾉": "艮","⾊": "色","⾍": "虫","⾎": "血","⾏": "行","⾐": "衣","⾒": "儿","⾓": "角","⾔": "言","⾕": "谷","⾖": "豆","⾚": "赤","⾛": "走","⾜": "足","⾝": "身","⾞": "车","⾟": "辛","⾠": "辰","⾢": "邑","⾣": "酉","⾤": "采","⾥": "里","⾦": "金","⾧": "长","⾨": "门","⾩": "阜","⾪": "隶","⾬": "雨","⾭": "青","⾮": "非","⾯": "面","⾰": "革","⾲": "韭","⾳": "音","⾴": "页","⾵": "风","⾶": "飞","⾷": "食","⾸": "首","⾹": "香","⾺": "马","⾻": "骨","⾼": "高","⿁": "鬼","⿂": "鱼","⿃": "鸟","⿄": "卤","⿅": "鹿","⿇": "麻","⿉": "黍","⿊": "黑","⿍": "鼎","⿎": "鼓","⿏": "鼠","⿐": "鼻","⿒": "齿","⿓": "龙","⿔":"龟","⿕":"仑"}
# 1. 创建一个新字典，将键（字符）转换为其 Unicode 码点（整数）
translation_map = {ord(key): value for key, value in kangxi_map.items()}

def replace_kangxi_with_pinyin(input_string):
    
    # 2. 使用 str.maketrans() 生成转换表
    translation_table = str.maketrans(translation_map)

    return input_string.translate(translation_table)


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_converter.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def print_dict(str):
    """格式化打印字典"""
    print(json.dumps(str, ensure_ascii=False, indent=2))


@dataclass
class TitleInfo:
    """标题信息"""
    level: int
    text: str
    page_num: int
    original_text: str  # TOC中的原始文本

    
def clean_title_text(title: str) -> str:
    """清理标题文本"""
    # 移除多余的空格和特殊字符
    title = re.sub(r'\s+', ' ', title.strip())
        
    # 移除可能的页码信息
    title = re.sub(r'\s*\.\.\.\s*\d+\s*$', '', title)
    title = re.sub(r'\s*\d+\s*$', '', title)
        
    return title.strip()
    
def extract_toc_titles(doc: pymupdf.Document) -> List[TitleInfo]:
    """提取TOC标题信息"""
    logger.info("正在提取TOC标题信息...")
    
    toc = doc.get_toc()
    if not toc:
        logger.warning("PDF中没有找到目录信息")
        return []
    
    titles = []
    for item in toc:
        level, title, page_num = item
        # print(item)
        
        # 清理标题文本
        title_info = TitleInfo(
            level=level,
            text=clean_title_text(title),
            page_num=page_num,
            original_text=title
        )
        titles.append(title_info)
    
    logger.info(f"提取到 {len(titles)} 个标题")
    
    return titles

def extract_text_between_substrings(main_string_list: List[str], start_substring: str, end_substring: str) -> str:
    """
    在行列表中提取两个子字符串之间的内容。
    注意: 这里的匹配逻辑是根据用户的原始代码实现的，用于处理标题行。
    """
    def _normalize_for_match(text: str) -> str:
        """规范化文本，移除 Markdown 标记和多余空格以便匹配"""
        return replace_kangxi_with_pinyin(re.sub(r'[*#\s]+', '', text))
    
    # 提取所有带有 # 或 ** 的行，并进行规范化
    hash_lines = []
    for i, line in enumerate(main_string_list):
        if '#' in line or '**' in line or '.':
            hash_lines.append((i, _normalize_for_match(line)))

    start_title = _normalize_for_match(start_substring)
    end_title = _normalize_for_match(end_substring)
    start_index = -1
    end_index = -1
    
    # 精准匹配
    for i, normalized_line in hash_lines: 
        if start_title == normalized_line:
            start_index = i
        if end_title == normalized_line:
            end_index = i

    # 模糊匹配
    if start_index == -1:
        for i, normalized_line in hash_lines:
            if start_title in normalized_line:
                start_index = i
                break
    if end_index == -1:
        for i, normalized_line in hash_lines:
            if end_title in normalized_line:
                end_index = i
                break
            
                
    if start_index == -1 or end_index==-1:
        logger.warning(f"无法在文档中找到开始标题: '{start_substring}'")
        print(start_index)
        print(end_index)
        return None
    
    
    result_content = main_string_list[start_index+1 : end_index]
    
    if start_index  == end_index:
        # 如果找不到结束标题或开始和结束标题相同，则提取到文档末尾
        end_index = len(main_string_list)-1
        result_content = main_string_list[start_index:]
        
    # #将头标题，尾标题，main_string_list，和最终content_content写入一个文件中，命名为chunk_标题
    # with open(f'chunk_{start_substring.replace("/","_")}.txt', 'w', encoding='utf-8') as f:
    #     f.write(f"头标题: {start_substring}\n")
    #     f.write(f"尾标题: {end_substring}\n")
    #     f.write(f"内容: {"".join(main_string_list)}\n")
    #     f.write(f"最终截取: {('\n'.join(result_content))}\n")
        
    return re.sub(r"\n-+\n","\n",("\n".join(result_content)).replace('#', '//#'))
    




def extract_content_optimized(file_url: str) -> str:
    """
    优化后的内容提取函数。
    一次性读取整个PDF并转换为Markdown，然后通过处理内存中的文本列表来提取内容。
    """
    logger.info(f"开始处理文件: {file_url}")
    
    # 步骤 1: 一次性读取整个PDF并转换为Markdown
    doc = pymupdf.open(file_url)
    
    # 获取完整的Markdown内容字符串
    markdown_chunks_list = pymupdf4llm.to_markdown(doc,page_chunks=True,show_progress=True)

    #兼容库读取page和index的偏移
    page_offset=-1
    try:
        page_offset=-1*int(markdown_chunks_list[0]['metadata']['page'])
    except Exception as e:
        logger.error(f"无法获取页码偏移量",e)
    logger.info(f'页面偏移量{page_offset}')
    result_content_list = []
    toc_list = []
    for markdown_chunk in markdown_chunks_list:
        toc_list.extend(markdown_chunk['toc_items'])
   
    # print(toc_list)
    for index,toc_item in enumerate(toc_list):
        
        start_title=toc_item
        start_index=toc_item[2]+page_offset
        all_lines=[]
        # 确定下一个标题作为结束标记
        if index < len(toc_list) - 1:
            
            end_title = toc_list[index + 1]
            end_index=end_title[2]+page_offset
            for md_chunk in markdown_chunks_list[start_index:end_index+1]:
                all_lines.extend(md_chunk['text'].split('\n\n'))
        else:
            # 最后一个标题，没有结束标记
            end_title = start_title

            for md_chunk in markdown_chunks_list[start_index:]:
                all_lines.extend(md_chunk['text'].split('\n\n'))
              
        content = extract_text_between_substrings(all_lines, start_title[1], end_title[1])
        
        if content is not None:
            # 如果成功提取到内容，则格式化并添加到结果列表
            markdown_heading = "#" * int(toc_item[0])
            result_content_list.append(f"{markdown_heading} {toc_item[1]}\n{content}")
        else:
            logger.warning(f"无法为标题 '{toc_item[1]}' 提取到内容。")
            
    return '\n'.join(result_content_list)

def process_single_pdf(pdf_file: Path):
    """
    处理单个PDF文件的函数，用于并行执行。
    """
    try:
        markdown = extract_content_optimized(str(pdf_file))
        OUT_DIR = pdf_file.parent / 'md'
        OUT_DIR.mkdir(exist_ok=True)
        md_path = OUT_DIR / f'{pdf_file.stem}.md'
        
        # 写入 Markdown 文件
        md_path.write_text(markdown, encoding='utf-8')
        logger.info(f'Markdown 文件已成功保存到: {md_path}')
        return f'成功处理文件: {pdf_file.name}'
    except Exception as e:
        logger.error(f"处理文件 {pdf_file.name} 时出错: {e}")
        return f'处理文件 {pdf_file.name} 失败'
    
    
class PDFConverter:
    """PDF转换器类，支持多种转换方式"""

    def __init__(self):
        """
        初始化PDF转换器
        """
        pass

    def has_toc(self, pdf_file: Path) -> bool:
        """检查PDF是否有目录信息"""
        try:
            if not pdf_file.exists():
                return False
            doc = pymupdf.open(str(pdf_file))
            toc = doc.get_toc()
            doc.close()
            return bool(toc)
        except Exception as e:
            logger.warning(f"检查TOC时出错: {e}")
            return False

    async def convert_with_native_method(self, pdf_file: Path) -> str:
        """使用原生方法（pymupdf4llm）转换PDF"""
        if not pdf_file.exists():
            raise FileNotFoundError(f"文件不存在: {pdf_file}")

        # 检查是否有TOC
        if not self.has_toc(pdf_file):
            raise ValueError(f"PDF中没有找到目录信息，无法使用原生方法: {pdf_file}")

        logger.info(f"使用原生方法转换PDF: {pdf_file}")

        # 在线程池中执行同步的原生方法
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
            markdown = await loop.run_in_executor(
                executor, extract_content_optimized, str(pdf_file)
            )

        return markdown

    async def convert_with_mineru_method(self, pdf_file: Path, output_dir: Path, max_retries: int = 2) -> str:
        """使用MinerU方法转换PDF"""
        if not pdf_file.exists():
            raise FileNotFoundError(f"文件不存在: {pdf_file}")

        logger.info(f"使用MinerU方法转换PDF: {pdf_file}")

        # 创建临时输出目录
        temp_output_dir = output_dir / "mineru_temp"
        temp_output_dir.mkdir(parents=True, exist_ok=True)

        last_error = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    logger.info(f"MinerU重试第 {attempt} 次: {pdf_file}")
                    # 清理之前的临时文件
                    if temp_output_dir.exists():
                        shutil.rmtree(temp_output_dir, ignore_errors=True)
                    temp_output_dir.mkdir(parents=True, exist_ok=True)
                    # 等待一段时间让系统释放内存
                    await asyncio.sleep(2)

                # 构建MinerU命令
                cmd = [
                    "uv", "run", "mineru",
                    "-p", str(pdf_file.resolve()),
                    "-o", str(temp_output_dir.resolve()),
                    "-m", "auto",
                    "-b", "pipeline"
                ]

                logger.info(f"执行MinerU命令: {' '.join(cmd)}")

                start_time = time.time()

                def run_mineru_sync():
                    """在线程池中同步执行MinerU"""
                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.STDOUT,
                        text=True,
                        bufsize=1,
                        universal_newlines=True,
                        cwd=str(Path.cwd())
                    )

                    stdout_lines = []
                    while True:
                        output = process.stdout.readline()
                        if output == '' and process.poll() is not None:
                            break
                        if output:
                            output = output.strip()
                            stdout_lines.append(output)

                    return_code = process.poll()
                    return return_code, stdout_lines

                # 在线程池中异步执行
                loop = asyncio.get_event_loop()
                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                    return_code, stdout_lines = await loop.run_in_executor(
                        executor, run_mineru_sync
                    )

                duration = time.time() - start_time

                # 记录输出（只记录关键信息，避免日志过多）
                error_lines = [line for line in stdout_lines if 'error' in line.lower() or 'exception' in line.lower()]
                if error_lines:
                    for error_line in error_lines[-5:]:  # 只记录最后5个错误
                        logger.warning(f"[MINERU_ERROR] {error_line}")

                logger.info(f"MinerU执行完成 - duration: {duration:.2f}s, return_code: {return_code}")

                if return_code == 0:
                    # 查找生成的markdown文件
                    markdown_files = []
                    for root, _, files in os.walk(temp_output_dir):
                        for file in files:
                            if file.endswith('.md'):
                                markdown_files.append(Path(root) / file)

                    if markdown_files:
                        # 合并所有markdown文件内容
                        combined_content = []
                        for md_file in sorted(markdown_files):
                            with open(md_file, 'r', encoding='utf-8', errors='ignore') as f:
                                content = f.read()
                                combined_content.append(content)

                        result = '\n\n'.join(combined_content)
                        logger.info(f"MinerU转换成功")
                        return result
                    else:
                        last_error = Exception("MinerU未生成markdown文件")
                        logger.warning(f"尝试 {attempt + 1}: MinerU未生成markdown文件")
                else:
                    last_error = Exception(f"MinerU执行失败 - return_code: {return_code}")
                    logger.warning(f"尝试 {attempt + 1}: MinerU执行失败 - return_code: {return_code}")

                # 检查是否是内存错误
                memory_error_keywords = ['memory', 'allocate', 'insufficient memory', 'out of memory']
                has_memory_error = any(
                    any(keyword in line.lower() for keyword in memory_error_keywords)
                    for line in stdout_lines
                )

                if has_memory_error and attempt < max_retries:
                    logger.warning(f"检测到内存错误，将在 {attempt + 1} 次重试")
                    continue
                elif not has_memory_error:
                    # 如果不是内存错误，不需要重试
                    break

            except Exception as e:
                last_error = e
                logger.error(f"尝试 {attempt + 1}: MinerU执行异常: {e}")
                if attempt < max_retries:
                    await asyncio.sleep(2)  # 等待后重试
                    continue
                else:
                    break

        # 所有重试都失败了
        try:
            if last_error:
                raise last_error
            else:
                raise Exception("MinerU转换失败，原因未知")
        finally:
            # 清理临时目录
            if temp_output_dir.exists():
                shutil.rmtree(temp_output_dir, ignore_errors=True)

    async def convert_pdf_to_markdown(self, pdf_file: Path, output_dir: Path) -> str:
        """
        智能PDF转换方法，根据TOC情况选择转换方式

        Args:
            pdf_file: PDF文件路径
            output_dir: 输出目录（用于MinerU临时文件）

        Returns:
            转换后的markdown内容
        """
        if not pdf_file.exists():
            raise FileNotFoundError(f"文件不存在: {pdf_file}")

        # 检查是否有TOC
        if self.has_toc(pdf_file):
            logger.info(f"PDF有TOC，使用原生方法转换: {pdf_file}")
            try:
                return await self.convert_with_native_method(pdf_file)
            except Exception as e:
                logger.warning(f"原生方法转换失败，尝试MinerU方法: {e}")
                return await self.convert_with_mineru_method(pdf_file, output_dir, max_retries=1)
        else:
            logger.info(f"PDF无TOC，使用MinerU方法转换: {pdf_file}")
            return await self.convert_with_mineru_method(pdf_file, output_dir, max_retries=1)


# 保持向后兼容的函数
def convert_pdf_to_markdown(pdf_file: Path) -> str:
    """向后兼容的函数，仅支持有TOC的PDF"""
    if not pdf_file.exists():
        raise FileNotFoundError(f"文件不存在: {pdf_file}")

    # 判断是否可以获取到toc
    doc = pymupdf.open(str(pdf_file))
    toc = doc.get_toc()
    doc.close()

    if not toc:
        raise ValueError(f"PDF中没有找到目录信息: {pdf_file}")

    markdown = extract_content_optimized(str(pdf_file))
    return markdown

