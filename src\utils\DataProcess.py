"""
数据处理工具模块
负责文件操作、雪花ID生成、文件上传下载等核心功能
"""

import os
import time
import shutil
import aiofiles
import requests
from typing import Dict, Any, Optional, Tuple
from pathlib import Path
from fastapi import UploadFile
from .config import config_manager

# 使用文件扩展名进行类型检测（不依赖magic库）


class SnowflakeIDGenerator:
    """雪花ID生成器"""
    
    def __init__(self, machine_id: int = 1, datacenter_id: int = 1):
        self.machine_id = machine_id
        self.datacenter_id = datacenter_id
        self.sequence = 0
        self.last_timestamp = -1
        
        # 各部分位数
        self.machine_id_bits = 5
        self.datacenter_id_bits = 5
        self.sequence_bits = 12
        
        # 最大值
        self.max_machine_id = -1 ^ (-1 << self.machine_id_bits)
        self.max_datacenter_id = -1 ^ (-1 << self.datacenter_id_bits)
        self.sequence_mask = -1 ^ (-1 << self.sequence_bits)
        
        # 位移
        self.machine_id_shift = self.sequence_bits
        self.datacenter_id_shift = self.sequence_bits + self.machine_id_bits
        self.timestamp_left_shift = self.sequence_bits + self.machine_id_bits + self.datacenter_id_bits
        
        # 起始时间戳 (2020-01-01)
        self.twepoch = 1577836800000
        
        if machine_id > self.max_machine_id or machine_id < 0:
            raise ValueError(f"机器ID必须在0到{self.max_machine_id}之间")
        if datacenter_id > self.max_datacenter_id or datacenter_id < 0:
            raise ValueError(f"数据中心ID必须在0到{self.max_datacenter_id}之间")
    
    def _current_millis(self) -> int:
        """获取当前毫秒时间戳"""
        return int(time.time() * 1000)
    
    def _til_next_millis(self, last_timestamp: int) -> int:
        """等待下一毫秒"""
        timestamp = self._current_millis()
        while timestamp <= last_timestamp:
            timestamp = self._current_millis()
        return timestamp
    
    def next_id(self) -> int:
        """生成下一个雪花ID"""
        timestamp = self._current_millis()
        
        if timestamp < self.last_timestamp:
            raise Exception("时钟回拨，拒绝生成ID")
        
        if self.last_timestamp == timestamp:
            self.sequence = (self.sequence + 1) & self.sequence_mask
            if self.sequence == 0:
                timestamp = self._til_next_millis(self.last_timestamp)
        else:
            self.sequence = 0
        
        self.last_timestamp = timestamp
        
        return ((timestamp - self.twepoch) << self.timestamp_left_shift) | \
               (self.datacenter_id << self.datacenter_id_shift) | \
               (self.machine_id << self.machine_id_shift) | \
               self.sequence


class DataProcess:
    """数据处理工具类"""

    def __init__(self):
        self.snowflake = SnowflakeIDGenerator()
        self.config = config_manager

        # 确保目录存在
        self.config._ensure_directories()
    

    
    async def write_file_tmp(self, file_content: bytes, raw_file_name: str, document_id: str) -> Dict[str, Any]:
        """文件tmp写入方法"""
        # 检查文件是否已存在
        if self.config.processing.skip_existing_files:
            existing_metadata = self.config.get_existing_file_metadata(document_id, raw_file_name)
            if existing_metadata:
                print(f"文件已存在，跳过处理: {raw_file_name}")
                return existing_metadata

        # 生成雪花ID和元数据
        snow_id = str(self.snowflake.next_id())+"_"+str(raw_file_name).rsplit('.',1)[0]
        file_ext = Path(raw_file_name).suffix
        tmp_file_name = f"{snow_id}{file_ext}"

        # 使用新的目录结构：document_id/snow_id/文件
        final_path = self.config.get_file_path(document_id, snow_id, tmp_file_name, "receive")
        final_path.parent.mkdir(parents=True, exist_ok=True)

        # 写入文件
        async with aiofiles.open(final_path, 'wb') as f:
            await f.write(file_content)

        # 创建文件元数据
        metadata = {
            "raw_file_name": raw_file_name,
            "file_type": self.config._get_file_type(str(final_path)),
            "snow_id": snow_id,
            "tmp_file_name": tmp_file_name,
            "timestamp": str(int(time.time())),
            "document_id": document_id,
            "file_path": str(final_path)
        }

        return metadata
    
    async def write_file_normal(self, file_path: str, content: bytes) -> bool:
        """文件普通写入方法"""
        try:
            file_path_obj = Path(file_path)
            file_path_obj.parent.mkdir(parents=True, exist_ok=True)
            
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(content)
            return True
        except Exception as e:
            print(f"文件写入失败: {e}")
            return False
    
    async def copy_file(self, src_path: str, document_id: str) -> Dict[str, Any]:
        """文件复制方法"""
        src_path_obj = Path(src_path)
        if not src_path_obj.exists():
            raise FileNotFoundError(f"源文件不存在: {src_path}")
        
        # 读取文件内容
        async with aiofiles.open(src_path, 'rb') as f:
            content = await f.read()
        
        # 调用tmp写入方法
        return await self.write_file_tmp(content, src_path_obj.name, document_id)
    
    async def read_file(self, file_path: str) -> bytes:
        """文件读取方法"""
        async with aiofiles.open(file_path, 'rb') as f:
            return await f.read()
    
    async def download_file(self, file_url: str, document_id: str) -> Dict[str, Any]:
        """文件下载方法"""
        try:
            response = requests.get(file_url, stream=True)
            response.raise_for_status()
            
            # 从URL获取文件名
            file_name = Path(file_url).name
            if not file_name or '.' not in file_name:
                # 如果无法从URL获取文件名，使用Content-Disposition
                content_disposition = response.headers.get('content-disposition')
                if content_disposition:
                    import re
                    filename_match = re.search(r'filename="?([^"]+)"?', content_disposition)
                    if filename_match:
                        file_name = filename_match.group(1)
                    else:
                        file_name = f"download_{int(time.time())}.bin"
                else:
                    file_name = f"download_{int(time.time())}.bin"
            
            # 下载文件内容
            content = response.content
            
            # 调用tmp写入方法
            return await self.write_file_tmp(content, file_name, document_id)
            
        except Exception as e:
            raise Exception(f"文件下载失败: {e}")
    
    async def parse_formdata_file(self, upload_file: UploadFile, document_id: str) -> Dict[str, Any]:
        """解析formdata文件方法"""
        try:
            # 确保文件名存在
            filename = upload_file.filename or "unknown_file"

            # 读取上传文件内容
            content = await upload_file.read()

            # 重置文件指针（如果需要的话）
            if hasattr(upload_file, 'seek'):
                try:
                    await upload_file.seek(0)
                except:
                    pass  # 忽略seek错误

            # 调用tmp写入方法
            return await self.write_file_tmp(content, filename, document_id)

        except Exception as e:
            raise Exception(f"FormData文件解析失败: {e}")
    
    async def copy_to_markdown_dir(self, metadata: Dict[str, Any], content: bytes) -> str:
        """将文件复制到markdown目录"""
        markdown_filename = f"{metadata['snow_id']}_markdown.md"
        markdown_path = self.config.get_file_path(
            metadata["document_id"],
            metadata["snow_id"],
            markdown_filename,
            "markdown"
        )
        markdown_path.parent.mkdir(parents=True, exist_ok=True)

        async with aiofiles.open(markdown_path, 'wb') as f:
            await f.write(content)

        return str(markdown_path)
